#!/bin/bash

# AWOS System Startup Script
# This script handles the container startup process including audio component download

set -e  # Exit on any error

echo "=== AWOS System Container Startup ==="
echo "Starting at $(date)"

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

log "Running as user: $(whoami)"
log "Working directory: $(pwd)"

# Show configuration
log "Configuration:"
log "  API Base URL: ${API_BASE_URL:-http://localhost:8000}"
log "  Volume: ${VOLUME:-80}%"
log "  Recording Enabled: ${ENABLE_RECORDING:-true}"
log "  Signal Detection Enabled: ${ENABLE_SIGNAL_DETECTION:-true}"
log "  Recording Storage: ${RECORDING_STORAGE_PATH:-/app/recordings}"
log "  S3 Upload Enabled: ${ENABLE_S3_UPLOAD:-false}"
if [ "${ENABLE_S3_UPLOAD:-false}" = "true" ]; then
    log "  Station Name: ${STATION_NAME:-UNKNOWN_STATION}"
    log "  S3 Bucket: ${S3_BUCKET_NAME:-recordings}"
    log "  S3 Endpoint: ${S3_ENDPOINT_URL:-not set}"
fi

# Ensure recordings directory exists and has proper permissions
RECORDING_DIR="${RECORDING_STORAGE_PATH:-/app/recordings}"
if [ ! -d "$RECORDING_DIR" ]; then
    log "Creating recordings directory: $RECORDING_DIR"
    mkdir -p "$RECORDING_DIR"
fi

# Set proper permissions for recordings directory
log "Setting permissions for recordings directory..."
chmod 777 "$RECORDING_DIR" 2>/dev/null || log "Warning: Could not set directory permissions"
touch "$RECORDING_DIR/.test" 2>/dev/null && rm "$RECORDING_DIR/.test" 2>/dev/null
if [ $? -eq 0 ]; then
    log "✓ Recordings directory is writable"
else
    log "⚠ Warning: Recordings directory may not be writable"
fi

# Check audio device access
log "Checking audio device access..."
if [ -d "/dev/snd" ]; then
    log "✓ Audio devices available: $(ls -la /dev/snd/ | wc -l) devices"
else
    log "⚠ Warning: /dev/snd not found - audio may not work"
fi

# Check Python environment
log "Python version: $(python --version)"
log "Checking Python dependencies..."

# Test critical imports
python -c "
import sys
try:
    import requests, pydub, numpy, scipy, sounddevice, boto3, gpiod, ffmpeg, m3u8
    print('✓ All Python dependencies available')

    # Test unified audio system modules
    from audio_event_manager import AudioEventManager
    from unified_signal_detector import UnifiedSignalDetector
    from recording_controller import RecordingController
    from awos_controller import AWOSController
    from audio_config import AudioConfig
    from s3_upload_manager import S3UploadManager
    from gpio_controller import GpioController
    from hls_audio_subscriber import HLSAudioSubscriber
    from hls_vps_uploader import HLSVPSUploader
    from awos_api_client import AWOSAPIClient
    print('✓ All unified audio system modules available')
except ImportError as e:
    print(f'✗ Missing dependency or module: {e}')
    sys.exit(1)
"

# Check network connectivity for API access
log "Checking network connectivity..."
API_URL="${API_BASE_URL:-http://localhost:8000}/health"
if python -c "import requests; requests.get('$API_URL', timeout=5)" 2>/dev/null; then
    log "✓ Network connectivity available for API access"
else
    log "⚠ Warning: Network connectivity issues - API access may fail"
fi

# Start the main application
log "Starting AWOS System..."
exec python /app/main.py