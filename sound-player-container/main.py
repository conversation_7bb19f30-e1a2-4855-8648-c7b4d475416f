#!/usr/bin/env python3
"""
Main Application for AWOS System on Raspberry Pi 5
"""

import os
import time
import subprocess
import logging
import sys
import signal
import threading
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor

from awos_api_client import AWOSAP<PERSON>lient
from audio_device_manager import AudioDeviceManager
from audio_event_manager import AudioEventManager
from gpio_controller import <PERSON><PERSON><PERSON>ontroll<PERSON>

from audio_config import AudioConfig
from s3_upload_manager import S3UploadManager
from unified_signal_detector import UnifiedSignalDetector
from recording_controller import RecordingController
from awos_controller import AWOSController
from hls_audio_subscriber import HLSAudioSubscriber
from hls_server import HLSServer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stdout,
    force=True
)
logger = logging.getLogger(__name__)

# Force stdout to be unbuffered
sys.stdout.reconfigure(line_buffering=True)
sys.stderr.reconfigure(line_buffering=True)

# Global reference to current player for HLS server access
_current_player = None



class WeatherSoundPlayer:
    def __init__(self, api_base_url=None):
        # Use configuration from AudioConfig
        self.volume = AudioConfig.VOLUME

        self.should_stop = False
        self.current_audio_file = "/tmp/current_weather.mp3"  # Changed to mp3 since API serves mp3
        self.current_process = None
        self.awos_requested = False
        self.weather_preparation_future = None
        self.executor = ThreadPoolExecutor(max_workers=1, thread_name_prefix="weather-prep")
        self.gpio_controller = GpioController()

        # Initialize AWOS API client
        self.awos_api_client = AWOSAPIClient(api_base_url)


        # Detect best audio configuration on startup using comprehensive detection
        audio_manager = AudioDeviceManager()
        self.input_device, self.audio_config = audio_manager.detect_devices()

        # Log the detected devices
        if self.input_device:
            logger.info(f"✓ Using audio input device: {self.input_device}")
        else:
            logger.warning("⚠ No audio input device detected - click detection may not work")

        if self.audio_config:
            logger.info(f"✓ Using audio output device: {self.audio_config}")
        else:
            logger.warning("⚠ No audio output device detected - playback may fail")
            self.audio_config = 'default'  # Fallback



        # Log and validate configuration
        AudioConfig.log_configuration()
        AudioConfig.validate_configuration()

        # Initialize audio event manager
        self.audio_event_manager = AudioEventManager(
            sample_rate=AudioConfig.SAMPLE_RATE,
            channels=AudioConfig.CHANNELS,
            chunk_size=AudioConfig.CHUNK_SIZE,
            freq_min_hz=AudioConfig.FREQ_MIN_HZ,
            freq_max_hz=AudioConfig.FREQ_MAX_HZ
        )

        # Initialize S3 upload manager (only if enabled)
        self.s3_upload_manager = None
        if AudioConfig.ENABLE_S3_UPLOAD:
            if not all([AudioConfig.S3_ACCESS_KEY_ID, AudioConfig.S3_SECRET_ACCESS_KEY,
                       AudioConfig.S3_ENDPOINT_URL, AudioConfig.STATION_NAME]):
                logger.warning("S3 upload enabled but missing required credentials/configuration")
            else:
                try:
                    self.s3_upload_manager = S3UploadManager(
                        access_key_id=AudioConfig.S3_ACCESS_KEY_ID,
                        secret_access_key=AudioConfig.S3_SECRET_ACCESS_KEY,
                        endpoint_url=AudioConfig.S3_ENDPOINT_URL,
                        bucket_name=AudioConfig.S3_BUCKET_NAME,
                        station_name=AudioConfig.STATION_NAME,
                        station_timezone=AudioConfig.STATION_TZ,
                        region_name=AudioConfig.S3_REGION_NAME,
                        max_retry_delay=AudioConfig.S3_MAX_RETRY_DELAY,
                        max_concurrent_uploads=AudioConfig.S3_MAX_CONCURRENT_UPLOADS
                    )
                    logger.info("S3 upload manager initialized successfully")
                except Exception as e:
                    logger.error(f"Failed to initialize S3 upload manager: {e}")
                    self.s3_upload_manager = None

        # Initialize unified signal detector
        self.unified_signal_detector = UnifiedSignalDetector(
            signal_threshold_high=AudioConfig.SIGNAL_THRESHOLD_HIGH,
            signal_threshold_low=AudioConfig.SIGNAL_THRESHOLD_LOW,
            click_min_duration=AudioConfig.CLICK_MIN_DURATION
        )
        self.audio_event_manager.add_subscriber(self.unified_signal_detector)

        # Initialize recording controller (only if enabled)
        self.recording_controller = None
        if AudioConfig.ENABLE_RECORDING:
            self.recording_controller = RecordingController(
                storage_path=AudioConfig.RECORDING_STORAGE_PATH,
                pre_roll_seconds=AudioConfig.PRE_ROLL_SECONDS,
                post_roll_seconds=AudioConfig.POST_ROLL_SECONDS,
                min_segment_duration=AudioConfig.MIN_SEGMENT_DURATION,
                max_segment_duration=AudioConfig.MAX_SEGMENT_DURATION,
                merge_gap_threshold=AudioConfig.MERGE_GAP_THRESHOLD,
                s3_upload_manager=self.s3_upload_manager
            )
            # Add recording controller as both signal event handler and audio event subscriber
            self.unified_signal_detector.add_handler(self.recording_controller)
            self.audio_event_manager.add_subscriber(self.recording_controller)

        # Initialize AWOS controller (only if enabled)
        self.awos_controller = None
        if AudioConfig.ENABLE_SIGNAL_DETECTION:
            self.awos_controller = AWOSController(
                awos_callback=self._trigger_awos,
                weather_prep_callback=self._start_weather_preparation,
                radio_check_callback=self._trigger_radio_check,
                click_min_duration=AudioConfig.CLICK_MIN_DURATION,
                click_max_duration=AudioConfig.CLICK_MAX_DURATION,
                click_cooldown=AudioConfig.CLICK_COOLDOWN,
                awos_click_count=AudioConfig.AWOS_CLICK_COUNT,
                radio_check_click_count=AudioConfig.RADIO_CHECK_CLICK_COUNT
            )
            self.unified_signal_detector.add_handler(self.awos_controller)

        # Initialize HLS streaming subscriber (only if enabled)
        self.hls_subscriber = None
        self.hls_server = None
        if AudioConfig.ENABLE_HLS_STREAMING:
            try:
                self.hls_subscriber = HLSAudioSubscriber()
                self.audio_event_manager.add_subscriber(self.hls_subscriber)
                logger.info("HLS streaming subscriber initialized and registered")

                # Start HLS HTTP server in a separate thread
                self.hls_server = HLSServer(port=8080)
                import threading

                def run_hls_server():
                    self.hls_server.start()

                self.hls_server_thread = threading.Thread(target=run_hls_server, daemon=True)
                self.hls_server_thread.start()

            except Exception as e:
                logger.error(f"Failed to initialize HLS streaming: {e}")
                self.hls_subscriber = None
                self.hls_server = None

        # Set the input device for the audio event manager
        if self.input_device:
            self.audio_event_manager.set_input_device(self.input_device)

        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)





    def _signal_handler(self, signum, _frame):
        """Handle shutdown signals gracefully"""
        logger.info(f"Received signal {signum}, shutting down...")
        self.should_stop = True

        # Stop GPIO controller
        self.gpio_controller.stop()

        # Shutdown S3 upload manager if active
        if self.s3_upload_manager:
            logger.info("Shutting down S3 upload manager...")
            self.s3_upload_manager.shutdown(timeout=30.0)

        # Shutdown executor
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)

        # Stop audio stream
        if self.audio_event_manager.is_running():
            self._stop_audio_processing()


        # Stop current playback process
        if self.current_process:
            try:
                self.current_process.terminate()
                self.current_process.wait(timeout=2)
            except subprocess.TimeoutExpired:
                self.current_process.kill()
            except Exception:
                pass

    def _start_weather_preparation(self):
        """Start weather preparation in background thread after 3rd click"""
        logger.info("Starting weather preparation...")
        self.weather_preparation_future = self.executor.submit(self._prepare_fresh_weather)

    def _prepare_fresh_weather(self):
        """Generate fresh weather report in background thread"""
        try:
            logger.info("Fetching fresh weather data...")
            return self.fetch_weather_report()
        except Exception as e:
            logger.error(f"Weather preparation failed: {e}")
            return False

    def _trigger_awos(self):
        """Callback method triggered when AWOS click sequence is detected"""
        logger.info("AWOS playback triggered by click detection!")
        self.awos_requested = True

    def _trigger_radio_check(self):
        """Callback method triggered when radio check click sequence is detected"""
        logger.info("🔊 RADIO CHECK REQUESTED")
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] Radio check requested - {AudioConfig.RADIO_CHECK_CLICK_COUNT} clicks detected")

        # TODO: Implement radio check functionality
        # This could include:
        # - Playing a radio check confirmation tone
        # - Logging radio check statistics
        # - Sending radio check data to external systems

    def _start_audio_processing(self):
        """Start the audio event manager for processing"""
        if self.input_device is None:
            logger.warning("No audio input device available - continuing with HLS streaming only")
            # For testing purposes, continue without audio input
            # The HLS server can still serve the player interface
            return True

        try:
            success = self.audio_event_manager.start()
            if success:
                logger.info(f"Audio processing started on device '{self.input_device}'")
                logger.info(f"Registered {self.audio_event_manager.get_subscriber_count()} audio subscribers")
            return success
        except Exception as e:
            logger.error(f"Failed to start audio processing: {e}")
            return False

    def _stop_audio_processing(self):
        """Stop the audio event manager"""
        try:
            self.audio_event_manager.stop()
            logger.info("Audio processing stopped")
        except Exception as e:
            logger.error(f"Error stopping audio processing: {e}")






    def fetch_weather_report(self):
        """Fetch weather report from API and save to audio file."""
        try:
            logger.info("Fetching AWOS audio from API...")

            # Fetch AWOS audio from weather-api-container
            success = self.awos_api_client.fetch_awos_audio(self.current_audio_file)

            if success:
                logger.info(f"✓ Weather report ready: {self.current_audio_file}")
                return True
            else:
                logger.error("Failed to fetch weather report from API")
                return False

        except Exception as e:
            logger.error(f"Failed to fetch weather report: {e}")
            return False

    def play_weather_report(self):
        """Play the current weather report once with relay control and AWOS recording."""
        import threading

        try:
            if not os.path.exists(self.current_audio_file):
                logger.error(f"Weather report file not found: {self.current_audio_file}")
                return False

            if self.should_stop:
                return False

            # Step 1: Engage relay before starting AWOS transmission
            if self.gpio_controller.is_initialized():
                if not self.gpio_controller.engage_relay():
                    logger.warning("Failed to engage relay, but continuing with AWOS playback")
            else:
                logger.warning("GPIO controller not initialized, AWOS will play without relay control")

            # Step 2: Suspend click detection during AWOS playback
            if self.awos_controller:
                self.awos_controller.suspend_click_detection()

            # Step 3: Start AWOS audio injection in a separate thread
            # This will feed AWOS audio chunks to the recording system
            injection_thread = threading.Thread(
                target=self.audio_event_manager.inject_awos_audio_chunks,
                args=(self.current_audio_file,),
                daemon=True
            )
            injection_thread.start()

            # Step 4: Play AWOS audio through speakers (existing logic)
            # Calculate volume multiplier (0.0 to 1.0)
            volume_multiplier = self.volume / 100.0

            if volume_multiplier == 1.0:
                # No volume adjustment needed, use direct aplay
                cmd = ['aplay', '-D', self.audio_config, str(self.current_audio_file)]

                self.current_process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.PIPE,
                    preexec_fn=os.setsid
                )
            else:
                # Use sox for volume control piped to aplay
                sox_cmd = ['sox', str(self.current_audio_file), '-t', 'wav', '-', 'vol', str(volume_multiplier)]
                aplay_cmd = ['aplay', '-D', self.audio_config, '-']

                # Create pipeline: sox | aplay
                sox_process = subprocess.Popen(
                    sox_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    preexec_fn=os.setsid
                )

                self.current_process = subprocess.Popen(
                    aplay_cmd,
                    stdin=sox_process.stdout,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.PIPE,
                    preexec_fn=os.setsid
                )

                # Close sox stdout in parent to allow proper pipeline termination
                sox_process.stdout.close()

            # Wait for completion
            try:
                return_code = self.current_process.wait()

                if return_code == 0:
                    logger.info(f"✓ Audio playback successful using device: {self.audio_config}")
                    playback_success = True
                else:
                    # Get error output
                    _, stderr = self.current_process.communicate(timeout=1)
                    error_msg = stderr.decode().strip() if stderr else "Unknown error"
                    logger.error(f"Audio playback failed (code {return_code}): {error_msg}")
                    playback_success = False

            except subprocess.TimeoutExpired:
                logger.error("Audio playback process hung, terminating...")
                self.current_process.terminate()
                playback_success = False

            # Wait for injection thread to complete
            injection_thread.join(timeout=5.0)
            if injection_thread.is_alive():
                logger.warning("AWOS audio injection thread did not complete in time")

            return playback_success

        except Exception as e:
            logger.error(f"Playback error: {e}")
            return False
        finally:
            # Step 5: Always clean up in the correct order
            # Resume click detection first
            if self.awos_controller:
                self.awos_controller.resume_click_detection()

            # Then disengage relay
            if self.gpio_controller.is_initialized():
                if not self.gpio_controller.disengage_relay():
                    logger.warning("Failed to disengage relay after AWOS transmission")

            self.current_process = None

    def run(self):
        """Main loop with click detection to trigger AWOS reports."""
        logger.info("AWOS System Starting...")
        logger.info(f"API Base URL: {self.awos_api_client.api_base_url}")
        logger.info(f"Volume: {self.volume}%")
        logger.info(f"Audio output device: {self.audio_config}")
        logger.info(f"Audio input device: {self.input_device}")

        # Ensure audio components directory exists
        audio_dir = Path("/app/audio_components")
        if not audio_dir.exists():
            logger.error(f"Audio components directory not found: {audio_dir}")
            logger.error("Please ensure AWOS audio components are mounted at /app/audio_components")
            return
        
        # Start GPIO Controller (skip if device not available for testing)
        try:
            self.gpio_controller.start()
        except FileNotFoundError as e:
            logger.warning(f"GPIO device not available: {e}")
            logger.warning("Continuing without GPIO support for testing purposes")

        # Start audio processing (click detection and recording)
        if not self._start_audio_processing():
            logger.error("Failed to start audio processing. Exiting.")
            self.gpio_controller.stop()
            return

        logger.info("AWOS System ready - unified audio event system active")
        if AudioConfig.ENABLE_SIGNAL_DETECTION:
            logger.info(f"AWOS detection: Looking for {AudioConfig.AWOS_CLICK_COUNT} clicks ({AudioConfig.CLICK_MIN_DURATION}-{AudioConfig.CLICK_MAX_DURATION}s each, {AudioConfig.CLICK_COOLDOWN}s cooldown)")
        if AudioConfig.ENABLE_RECORDING:
            logger.info(f"Audio recording: Saving segments ≥{AudioConfig.CLICK_MIN_DURATION}s to {AudioConfig.RECORDING_STORAGE_PATH}")
        logger.info(f"Signal thresholds: High={AudioConfig.SIGNAL_THRESHOLD_HIGH}, Low={AudioConfig.SIGNAL_THRESHOLD_LOW}")

        consecutive_failures = 0
        max_failures = 5

        try:
            while not self.should_stop:
                try:
                    # Audio processing is now handled by the AudioEventManager
                    # in separate threads, so we just need to handle AWOS requests

                    # Check if AWOS was requested
                    if self.awos_requested:
                        self.awos_requested = False

                        # Check if we have prepared weather data from background thread
                        weather_ready = False
                        if self.weather_preparation_future:
                            if self.weather_preparation_future.done():
                                # Weather preparation completed
                                try:
                                    weather_ready = self.weather_preparation_future.result()
                                    if weather_ready:
                                        logger.info("✓ Using pre-prepared weather data")
                                        consecutive_failures = 0
                                    else:
                                        logger.warning("Pre-prepared weather data failed")
                                except Exception as e:
                                    logger.error(f"Error getting prepared weather: {e}")
                                finally:
                                    self.weather_preparation_future = None
                            else:
                                # Weather preparation still in progress, wait briefly
                                logger.info("Waiting for weather preparation to complete...")
                                try:
                                    weather_ready = self.weather_preparation_future.result(timeout=2.0)
                                    if weather_ready:
                                        logger.info("✓ Weather preparation completed just in time")
                                        consecutive_failures = 0
                                    else:
                                        logger.warning("Weather preparation failed")
                                except Exception as e:
                                    logger.warning(f"Weather preparation timeout or error: {e}")
                                finally:
                                    self.weather_preparation_future = None

                        # Fallback: Fetch fresh weather if no prepared data available
                        if not weather_ready:
                            logger.info("Fetching fresh weather data (fallback)...")
                            if self.fetch_weather_report():
                                consecutive_failures = 0
                                logger.info("✓ Fresh weather report fetched")
                                weather_ready = True
                            else:
                                consecutive_failures += 1
                                logger.error(f"✗ Weather report fetch failed ({consecutive_failures}/{max_failures})")

                        # Play the weather report if we have valid data
                        if weather_ready:
                            logger.info("Playing AWOS report...")
                            if self.play_weather_report():
                                logger.info("✓ AWOS report played successfully")
                                consecutive_failures = 0  # Reset on successful playback
                            else:
                                consecutive_failures += 1
                                logger.error(f"✗ Failed to play AWOS report ({consecutive_failures}/{max_failures})")
                        else:
                            consecutive_failures += 1
                            logger.error("✗ No valid weather data available for AWOS playback")



                    # Small sleep to prevent busy waiting
                    time.sleep(0.1)

                except KeyboardInterrupt:
                    logger.info("Keyboard interrupt received, stopping...")
                    break
                except Exception as e:
                    logger.error(f"Unexpected error in AWOS system: {e}")
                    consecutive_failures += 1
                    time.sleep(1)  # Wait before retrying

                    if consecutive_failures >= max_failures:
                        logger.error("Too many consecutive failures, but continuing to listen...")
                        consecutive_failures = 0  # Reset to continue trying

        finally:
            # Clean up audio processing
            try:
                self._stop_audio_processing()
            except Exception as e:
                logger.error(f"Error stopping audio processing: {e}")

            # Clean up executor
            try:
                self.executor.shutdown(wait=True, timeout=5)
                logger.info("Weather preparation executor stopped")
            except Exception as e:
                logger.error(f"Error stopping executor: {e}")
            
            # Stop GPIO controller
            self.gpio_controller.stop()

            # Stop LL-HLS server
            if self.hls_server:
                try:
                    # The server will stop when the thread is terminated
                    logger.info("LL-HLS server stopped")
                except Exception as e:
                    logger.error(f"Error stopping LL-HLS server: {e}")

        logger.info("AWOS System stopped")


def main():
    """Main entry point."""
    global _current_player
    logger.info("Starting AWOS System")

    # Get API base URL from environment
    api_base_url = os.getenv("API_BASE_URL", "https://awosnew.skytraces.com")
    logger.info(f"Using weather API at: {api_base_url}")

    _current_player = WeatherSoundPlayer(api_base_url=api_base_url)
    _current_player.run()


if __name__ == "__main__":
    main()