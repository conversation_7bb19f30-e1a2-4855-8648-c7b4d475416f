#!/usr/bin/env python3
"""
Audio Configuration - Centralized configuration for the audio event system

This module provides configuration constants and environment variable handling
for the audio event system components.

Environment Variables:
    # Core System
    VOLUME: Audio volume 0-100 (default: 80)
    API_BASE_URL: Base URL for weather API (default: http://localhost:8000)

    # Audio Processing
    SAMPLE_RATE: Audio sample rate in Hz (default: 48000 - high quality for detection)
    CHANNELS: Number of audio channels (default: 1)
    CHUNK_SIZE: Audio chunk size in samples (default: 1024)
    FREQ_MIN_HZ: Minimum frequency for analysis (default: 200)
    FREQ_MAX_HZ: Maximum frequency for analysis (default: 3500)

    # Signal Detection
    SIGNAL_THRESHOLD_HIGH: High threshold for signal start (default: 50000)
    SIGNAL_THRESHOLD_LOW: Low threshold for signal end (default: 10000)
    CLICK_MIN_DURATION: Minimum click duration in seconds (default: 0.1)
    CLICK_MAX_DURATION: Maximum click duration in seconds (default: 0.6)
    CLICK_MAX_GAP: Maximum gap between clicks in seconds (default: 1.0) [DEPRECATED - use CLICK_COOLDOWN]
    CLICK_COOLDOWN: Unified timeout for click sequences in seconds (default: 1.0)
                   - Serves as max gap between consecutive clicks
                   - Also serves as silence period to finalize sequence
                   - Valid clicks within this time increment the counter
                   - Invalid clicks within this time reset the counter
                   - No clicks within this time finalize the sequence
    AWOS_CLICK_COUNT: Number of clicks for AWOS sequence (default: 3)
    RADIO_CHECK_CLICK_COUNT: Number of clicks for radio check sequence (default: 4)

    # Recording
    RECORDING_STORAGE_PATH: Directory for recordings (default: /app/recordings)
    PRE_ROLL_SECONDS: Pre-roll recording time (default: 0.5)
    POST_ROLL_SECONDS: Post-roll recording time (default: 1.0)
    MIN_SEGMENT_DURATION: Minimum recording duration (default: 0.1)
    MAX_SEGMENT_DURATION: Maximum recording duration (default: 30.0)
    MERGE_GAP_THRESHOLD: Gap threshold for merging recordings (default: 2.0)
    STATION_TZ: Timezone for recording timestamps (default: UTC)

    # Feature Toggles
    ENABLE_RECORDING: Enable audio recording (default: true)
    ENABLE_SIGNAL_DETECTION: Enable signal detection (default: true)

    # S3 Cloud Storage
    ENABLE_S3_UPLOAD: Enable S3 cloud storage uploads (default: false)
    S3_ACCESS_KEY_ID: S3 access key ID
    S3_SECRET_ACCESS_KEY: S3 secret access key
    S3_ENDPOINT_URL: S3 endpoint URL (for Cloudflare R2)
    S3_BUCKET_NAME: S3 bucket name (default: recordings)
    S3_REGION_NAME: S3 region name (default: auto for Cloudflare R2)
    STATION_NAME: Station name for S3 folder structure
    S3_MAX_RETRY_DELAY: Maximum retry delay in seconds (default: 300)
    S3_MAX_CONCURRENT_UPLOADS: Maximum concurrent uploads (default: 3)

    # HLS Streaming
    ENABLE_HLS_STREAMING: Enable HLS streaming (default: false)
    HLS_SEGMENT_DURATION: HLS segment duration in seconds (default: 2.0)
    HLS_PLAYLIST_SIZE: Number of segments to keep in playlist (default: 6)
    HLS_OUTPUT_PATH: Directory for HLS output files (default: /app/hls)
    HLS_AUDIO_BITRATE: Audio bitrate for HLS segments in kbps (default: 128)
    HLS_AUDIO_CODEC: Audio codec for HLS segments (default: aac)
    HLS_VPS_UPLOAD_URL: VPS endpoint for uploading HLS files (default: empty)
    HLS_VPS_AUTH_TOKEN: Authentication token for VPS uploads (default: empty)


"""

import os
import logging

logger = logging.getLogger(__name__)


class AudioConfig:
    """Centralized configuration for audio event system."""

    # Core system settings
    VOLUME = int(os.getenv('VOLUME', '80'))

    # Audio processing settings (48kHz for high-quality detection, downsample for storage)
    SAMPLE_RATE = int(os.getenv('SAMPLE_RATE', '48000'))  # 48kHz for optimal signal detection
    CHANNELS = int(os.getenv('CHANNELS', '1'))
    CHUNK_SIZE = int(os.getenv('CHUNK_SIZE', '1024'))
    FREQ_MIN_HZ = float(os.getenv('FREQ_MIN_HZ', '200'))
    FREQ_MAX_HZ = float(os.getenv('FREQ_MAX_HZ', '3500'))

    # Signal detection thresholds
    SIGNAL_THRESHOLD_HIGH = float(os.getenv('SIGNAL_THRESHOLD_HIGH', '50000'))
    SIGNAL_THRESHOLD_LOW = float(os.getenv('SIGNAL_THRESHOLD_LOW', '10000'))
    CLICK_MIN_DURATION = float(os.getenv('CLICK_MIN_DURATION', '0.1'))
    CLICK_MAX_DURATION = float(os.getenv('CLICK_MAX_DURATION', '0.6'))
    CLICK_MAX_GAP = float(os.getenv('CLICK_MAX_GAP', '1.0'))
    CLICK_COOLDOWN = float(os.getenv('CLICK_COOLDOWN', '1.0'))

    # Click pattern configuration
    AWOS_CLICK_COUNT = int(os.getenv('AWOS_CLICK_COUNT', '3'))
    RADIO_CHECK_CLICK_COUNT = int(os.getenv('RADIO_CHECK_CLICK_COUNT', '4'))

    # Recording settings
    RECORDING_STORAGE_PATH = os.getenv('RECORDING_STORAGE_PATH', '/app/recordings')
    PRE_ROLL_SECONDS = float(os.getenv('PRE_ROLL_SECONDS', '0.5'))
    POST_ROLL_SECONDS = float(os.getenv('POST_ROLL_SECONDS', '1.0'))
    MIN_SEGMENT_DURATION = float(os.getenv('MIN_SEGMENT_DURATION', '0.1'))
    MAX_SEGMENT_DURATION = float(os.getenv('MAX_SEGMENT_DURATION', '30.0'))
    MERGE_GAP_THRESHOLD = float(os.getenv('MERGE_GAP_THRESHOLD', '2.0'))

    # Timezone settings
    STATION_TZ = os.getenv('STATION_TZ', 'UTC')

    # Feature toggles
    ENABLE_RECORDING = os.getenv('ENABLE_RECORDING', 'true').lower() == 'true'
    ENABLE_SIGNAL_DETECTION = os.getenv('ENABLE_SIGNAL_DETECTION', 'true').lower() == 'true'

    # S3 Cloud Storage settings
    ENABLE_S3_UPLOAD = os.getenv('ENABLE_S3_UPLOAD', 'false').lower() == 'true'
    S3_ACCESS_KEY_ID = os.getenv('S3_ACCESS_KEY_ID', '')
    S3_SECRET_ACCESS_KEY = os.getenv('S3_SECRET_ACCESS_KEY', '')
    S3_ENDPOINT_URL = os.getenv('S3_ENDPOINT_URL', '')
    S3_BUCKET_NAME = os.getenv('S3_BUCKET_NAME', 'recordings')
    S3_REGION_NAME = os.getenv('S3_REGION_NAME', 'auto')
    STATION_NAME = os.getenv('STATION_NAME', 'UNKNOWN_STATION')
    S3_MAX_RETRY_DELAY = float(os.getenv('S3_MAX_RETRY_DELAY', '300.0'))
    S3_MAX_CONCURRENT_UPLOADS = int(os.getenv('S3_MAX_CONCURRENT_UPLOADS', '3'))

    # HLS Streaming settings
    ENABLE_HLS_STREAMING = os.getenv('ENABLE_HLS_STREAMING', 'false').lower() == 'true'
    HLS_SEGMENT_DURATION = float(os.getenv('HLS_SEGMENT_DURATION', '2.0'))
    HLS_PLAYLIST_SIZE = int(os.getenv('HLS_PLAYLIST_SIZE', '6'))
    HLS_OUTPUT_PATH = os.getenv('HLS_OUTPUT_PATH', '/app/hls')
    HLS_AUDIO_BITRATE = int(os.getenv('HLS_AUDIO_BITRATE', '128'))
    HLS_AUDIO_CODEC = os.getenv('HLS_AUDIO_CODEC', 'aac')
    HLS_VPS_UPLOAD_URL = os.getenv('HLS_VPS_UPLOAD_URL', '')
    HLS_VPS_AUTH_TOKEN = os.getenv('HLS_VPS_AUTH_TOKEN', '')


    
    @classmethod
    def log_configuration(cls):
        """Log the current configuration settings."""
        logger.info("Audio Event System Configuration:")
        logger.info(f"  Volume: {cls.VOLUME}%")
        logger.info(f"  Sample Rate: {cls.SAMPLE_RATE} Hz")
        logger.info(f"  Channels: {cls.CHANNELS}")
        logger.info(f"  Chunk Size: {cls.CHUNK_SIZE} samples")
        logger.info(f"  Frequency Range: {cls.FREQ_MIN_HZ}-{cls.FREQ_MAX_HZ} Hz")
        logger.info(f"  Signal Thresholds: {cls.SIGNAL_THRESHOLD_LOW}-{cls.SIGNAL_THRESHOLD_HIGH}")
        logger.info(f"  Click Duration: {cls.CLICK_MIN_DURATION}-{cls.CLICK_MAX_DURATION}s")
        logger.info(f"  Click Max Gap: {cls.CLICK_MAX_GAP}s [DEPRECATED - use CLICK_COOLDOWN]")
        logger.info(f"  Click Cooldown: {cls.CLICK_COOLDOWN}s")
        logger.info(f"  AWOS Click Count: {cls.AWOS_CLICK_COUNT}")
        logger.info(f"  Radio Check Click Count: {cls.RADIO_CHECK_CLICK_COUNT}")
        logger.info(f"  Recording Storage: {cls.RECORDING_STORAGE_PATH}")
        logger.info(f"  Pre/Post Roll: {cls.PRE_ROLL_SECONDS}s/{cls.POST_ROLL_SECONDS}s")
        logger.info(f"  Recording Enabled: {cls.ENABLE_RECORDING}")
        logger.info(f"  Signal Detection Enabled: {cls.ENABLE_SIGNAL_DETECTION}")
    
    @classmethod
    def validate_configuration(cls):
        """Validate configuration values and log warnings for invalid settings."""
        warnings = []

        if cls.SAMPLE_RATE not in [8000, 16000, 22050, 44100, 48000]:
            warnings.append(f"Unusual sample rate: {cls.SAMPLE_RATE} Hz")

        if cls.CHANNELS not in [1, 2]:
            warnings.append(f"Unusual channel count: {cls.CHANNELS}")

        if cls.CHUNK_SIZE < 64 or cls.CHUNK_SIZE > 8192:
            warnings.append(f"Unusual chunk size: {cls.CHUNK_SIZE}")

        if cls.FREQ_MIN_HZ >= cls.FREQ_MAX_HZ:
            warnings.append(f"Invalid frequency range: {cls.FREQ_MIN_HZ}-{cls.FREQ_MAX_HZ} Hz")

        if cls.SIGNAL_THRESHOLD_LOW >= cls.SIGNAL_THRESHOLD_HIGH:
            warnings.append(f"Invalid threshold range: {cls.SIGNAL_THRESHOLD_LOW}-{cls.SIGNAL_THRESHOLD_HIGH}")

        if cls.CLICK_MIN_DURATION >= cls.CLICK_MAX_DURATION:
            warnings.append(f"Invalid click duration range: {cls.CLICK_MIN_DURATION}-{cls.CLICK_MAX_DURATION}s")

        if cls.PRE_ROLL_SECONDS < 0 or cls.POST_ROLL_SECONDS < 0:
            warnings.append(f"Negative roll times: pre={cls.PRE_ROLL_SECONDS}s, post={cls.POST_ROLL_SECONDS}s")

        if cls.VOLUME < 0 or cls.VOLUME > 100:
            warnings.append(f"Invalid volume: {cls.VOLUME}% (should be 0-100)")

        if warnings:
            logger.warning("Configuration validation warnings:")
            for warning in warnings:
                logger.warning(f"  - {warning}")
        else:
            logger.info("✓ Configuration validation passed")
